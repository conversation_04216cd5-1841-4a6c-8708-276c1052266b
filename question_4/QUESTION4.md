
Question 4:
You're working on a financial transaction system with the following schema:
```sql
CREATE TABLE transactions (
    transaction_id UUID PRIMARY KEY,
    account_number VARCHAR(50),
    transaction_amount DECIMAL(19, 4),
    transaction_type VARCHAR(20),
    booking_date DATE,
    entry_date TIMESTAMP
);

CREATE INDEX idx_account_number ON transactions(account_number);
```

The system has grown significantly over time and now contains:
• Over 10 million account numbers
• Some accounts have more than 1 million transactions
• The total table size exceeds several billion rows
Currently, the application uses this query to retrieve paginated transactions for a specific account:

```sql
SELECT * FROM transactions
WHERE account_number = ?
ORDER BY entry_date DESC
LIMIT 20 OFFSET ?;
```

This query has become increasingly slow as the system has grown, particularly for accounts with large
numbers of transactions.
Provide suggestion to optimize this query