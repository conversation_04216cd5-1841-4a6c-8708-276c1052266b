The symptoms you are seeing are almost always caused by two things:

The database must read a very large number of rows (or index entries) before it can return the requested page.
The database has to fetch the same long list over and over again when the user asks for a page with a big OFFSET.
Below is a pragmatic, step-by-step plan that removes both problems.  Each step is independent (you can stop when performance is “good enough”), but together they typically bring sub-millisecond response times even with millions of rows per account.

───────────────────────────────────

Replace OFFSET-based paging with key-set (cursor) paging ─────────────────────────────────── OFFSET forces the server to count/scan every skipped row on each request. Instead use “give me the next 20 rows after the last row I already have”.
First page

SELECT transaction_id,
       transaction_amount,
       transaction_type,
       booking_date,
       entry_date
FROM   transactions
WHERE  account_number = :acct
ORDER  BY entry_date DESC, transaction_id DESC       -- tie-breaker
LIMIT  20;
Subsequent pages

SELECT transaction_id,
       transaction_amount,
       transaction_type,
       booking_date,
       entry_date
FROM   transactions
WHERE  account_number = :acct
  AND (entry_date, transaction_id) < (:last_entry_date, :last_txn_id)
ORDER  BY entry_date DESC, transaction_id DESC
LIMIT  20;
Benefits
• The database stops scanning at exactly 20 rows, no matter how “deep” in the history you are.
• There is no re-reading of the same rows on every page.

───────────────────────────────────
2. Create a composite, covering index that matches the new access pattern
───────────────────────────────────

-- DESC order so the newest rows are together at the top of the index
CREATE INDEX idx_acct_entry_desc
          ON transactions (account_number, entry_date DESC, transaction_id DESC)
          INCLUDE (transaction_amount, transaction_type, booking_date);
Why it helps
• The filter column (account_number) is first, the sort columns follow → index can satisfy both WHERE and ORDER BY.
• DESC puts the newest rows (the ones users ask for most often) adjacent, making the index smaller to scan.
• INCLUDE makes the query “index-only” (no trips to the table heap), cutting I/O further.

───────────────────────────────────
3. Physically cluster / reorder the table (optional but valuable)
───────────────────────────────────
If the engine supports it (e.g. PostgreSQL’s CLUSTER, SQL Server’s clustered index), make the composite index the physical order of the table.
• Sequential access for a single account becomes almost contiguous on disk or in SSD pages.
• VACUUM/auto-cluster periodically to keep the order fresh.

───────────────────────────────────
4. Partition for manageability and further pruning (optional)
───────────────────────────────────
Very large installations (billions of rows) rarely keep everything in one heap:

a. Hash/list partition by account_number
– Guarantees each query touches only one small partition.
b. Range partition by entry_date (e.g. monthly)
– Makes archive/roll-off cheap and keeps hot partitions in cache.

Create the same composite index inside each partition.

───────────────────────────────────
5. Additional fine-tuning ideas
───────────────────────────────────
• Return only the columns the UI really needs (you almost never need SELECT *).
• Compress older partitions or move them to cheaper storage; they are rarely queried.
• Keep entry_date immutable; if back-dating is required, write once, don’t update.
• Monitor dead tuples / page splits; schedule VACUUM or REINDEX when needed.
• Place the WAL / transaction log on SSD; write amplification is high in a busy ledger.

───────────────────────────────────
Expected outcome
───────────────────────────────────
With the composite covering index and key-set pagination alone, most systems see queries that used to take multiple seconds drop to tens–hundreds of microseconds for the first page and remain flat (≈ same latency) for every subsequent page, even with >1 M rows per account and billions total.

Implement each step, measure, and stop as soon as the SLO is met.