import uuid, random
from datetime import datetime, timedelta
import psycopg2

conn = psycopg2.connect(
    dbname="transactions_db",
    user="postgres",
    password="postgres",
    host="localhost",
    port=5432
)

cur = conn.cursor()

base_date = datetime(2023, 1, 1)
account = 'A123'

for i in range(1_000_000):
    tx_id = str(uuid.uuid4())
    tx_amt = round(random.uniform(-5000, 5000), 2)
    tx_type = random.choice(['credit', 'debit'])
    book_date = base_date.date()
    entry_dt = base_date + timedelta(seconds=i * 60)

    cur.execute("""
        INSERT INTO transactions (transaction_id, account_number, transaction_amount,
                                  transaction_type, booking_date, entry_date)
        VALUES (%s, %s, %s, %s, %s, %s);
    """, (tx_id, account, tx_amt, tx_type, book_date, entry_dt))

    if i % 10000 == 0:
        conn.commit()

conn.commit()
cur.close()
conn.close()
