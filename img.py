from PIL import Image

def resize_to_fit_a4(image_path, output_path):
    img = Image.open(image_path)

    max_w, max_h = 595, 842
    img_w, img_h = img.size

    ratio = min(max_w / img_w, max_h / img_h)
    new_size = (int(img_w * ratio), int(img_h * ratio))

    resized = img.resize(new_size, Image.Resampling.LANCZOS)

    if resized.mode in ("RGBA", "P"):
        resized = resized.convert("RGB")

    resized.save(output_path)

resize_to_fit_a4("notification_delivery.png", "resized.jpg")
