"""
Comprehensive tests for the notification system.
"""
import pytest
from datetime import datetime

from solution import (
    NotificationChannel, NotificationStatus, UserPreferences,
    NotificationMessage, NotificationRequest, InMemoryUserPreferenceRepository,
    InMemoryNotificationRepository, EmailProvider, SMSProvider, NotificationService
)


class TestUserPreferences:
    """Test UserPreferences model."""
    
    def test_is_channel_enabled(self):
        """Test channel enablement check."""
        prefs = UserPreferences(
            user_id="test_user",
            enabled_channels=[NotificationChannel.EMAIL],
            email_address="<EMAIL>"
        )
        
        assert prefs.is_channel_enabled(NotificationChannel.EMAIL) is True
        assert prefs.is_channel_enabled(NotificationChannel.SMS) is False
    
    def test_get_contact_info(self):
        """Test contact info retrieval."""
        prefs = UserPreferences(
            user_id="test_user",
            enabled_channels=[NotificationChannel.EMAIL, NotificationChannel.SMS],
            email_address="<EMAIL>",
            phone_number="+**********"
        )
        
        assert prefs.get_contact_info(NotificationChannel.EMAIL) == "<EMAIL>"
        assert prefs.get_contact_info(NotificationChannel.SMS) == "+**********"


class TestNotificationRequest:
    """Test NotificationRequest validation."""

    def test_valid_request(self):
        """Test valid notification request."""
        request = NotificationRequest(
            user_id="test_user",
            subject="Test Subject",
            content="Test content"
        )
        assert request.user_id == "test_user"
        assert request.subject == "Test Subject"
        assert request.content == "Test content"

    def test_empty_user_id(self):
        """Test validation fails for empty user_id."""
        with pytest.raises(ValueError, match="user_id is required"):
            NotificationRequest(
                user_id="",
                subject="Test",
                content="Test"
            )

    def test_empty_subject(self):
        """Test validation fails for empty subject."""
        with pytest.raises(ValueError, match="subject is required"):
            NotificationRequest(
                user_id="test_user",
                subject="",
                content="Test"
            )

    def test_empty_content(self):
        """Test validation fails for empty content."""
        with pytest.raises(ValueError, match="content is required"):
            NotificationRequest(
                user_id="test_user",
                subject="Test",
                content=""
            )



    def test_invalid_preferred_channels(self):
        """Test validation fails for invalid preferred channels."""
        with pytest.raises(ValueError, match="preferred_channels must contain only NotificationChannel values"):
            NotificationRequest(
                user_id="test_user",
                subject="Test",
                content="Test",
                preferred_channels=["invalid"]
            )


class TestInMemoryRepositories:
    """Test in-memory repository implementations."""
    
    def test_user_preference_repository(self):
        """Test user preference repository operations."""
        repo = InMemoryUserPreferenceRepository()
        
        prefs = UserPreferences(
            user_id="test_user",
            enabled_channels=[NotificationChannel.EMAIL],
            email_address="<EMAIL>"
        )
        
        # Test save and get
        assert repo.save_user_preferences(prefs) is True
        retrieved = repo.get_user_preferences("test_user")
        assert retrieved is not None
        assert retrieved.user_id == "test_user"
        assert retrieved.email_address == "<EMAIL>"
        
        # Test non-existent user
        assert repo.get_user_preferences("non_existent") is None
    
    def test_notification_repository(self):
        """Test notification repository operations."""
        repo = InMemoryNotificationRepository()
        
        message = NotificationMessage(
            message_id="test_msg",
            user_id="test_user",
            subject="Test",
            content="Test content",
            channel=NotificationChannel.EMAIL,
            recipient="<EMAIL>",
            created_at=datetime.now()
        )
        
        # Test save and get
        assert repo.save_notification(message) is True
        retrieved = repo.get_notification("test_msg")
        assert retrieved is not None
        assert retrieved.message_id == "test_msg"
        
        # Test status update
        assert repo.update_notification_status("test_msg", "sent") is True
        updated = repo.get_notification("test_msg")
        assert updated.status == NotificationStatus.SENT
        assert updated.sent_at is not None


class TestNotificationProviders:
    """Test notification provider implementations."""
    
    def test_email_provider_success(self):
        """Test successful email sending."""
        provider = EmailProvider()

        message = NotificationMessage(
            message_id="test_msg",
            user_id="test_user",
            subject="Test",
            content="Test content",
            channel=NotificationChannel.EMAIL,
            recipient="<EMAIL>",
            created_at=datetime.now()
        )

        assert provider.channel_type == NotificationChannel.EMAIL
        assert provider.is_available() is True

        result = provider.send_notification(message)

        assert result is True
    
    def test_email_provider_failure(self):
        """Test email sending failure with invalid email."""
        provider = EmailProvider()

        message = NotificationMessage(
            message_id="test_msg",
            user_id="test_user",
            subject="Test",
            content="Test content",
            channel=NotificationChannel.EMAIL,
            recipient="invalid_email",  # Invalid email without @
            created_at=datetime.now()
        )

        result = provider.send_notification(message)

        assert result is False
    
    def test_sms_provider_success(self):
        """Test successful SMS sending."""
        provider = SMSProvider()

        message = NotificationMessage(
            message_id="test_msg",
            user_id="test_user",
            subject="Test",
            content="Test content",
            channel=NotificationChannel.SMS,
            recipient="+**********",
            created_at=datetime.now()
        )

        assert provider.channel_type == NotificationChannel.SMS
        assert provider.is_available() is True

        result = provider.send_notification(message)

        assert result is True


class TestNotificationService:
    """Test the main notification service."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.user_repo = InMemoryUserPreferenceRepository()
        self.notification_repo = InMemoryNotificationRepository()
        self.service = NotificationService(self.user_repo, self.notification_repo)
        
        # Register providers
        self.email_provider = EmailProvider()
        self.sms_provider = SMSProvider()
        self.service.register_channel_provider(self.email_provider)
        self.service.register_channel_provider(self.sms_provider)
        
        # Set up test user
        self.test_user_prefs = UserPreferences(
            user_id="test_user",
            enabled_channels=[NotificationChannel.EMAIL, NotificationChannel.SMS],
            email_address="<EMAIL>",
            phone_number="+**********"
        )
        self.user_repo.save_user_preferences(self.test_user_prefs)
    
    def test_send_notification_both_channels(self):
        """Test sending notification through both channels."""
        request = NotificationRequest(
            user_id="test_user",
            subject="Test Notification",
            content="This is a test notification."
        )

        message_ids = self.service.send_notification(request)

        assert len(message_ids) == 2  # Both email and SMS

        # Check that messages were saved
        for message_id in message_ids:
            message = self.service.get_notification_status(message_id)
            assert message is not None
            assert message.status == NotificationStatus.SENT
    
    def test_send_notification_preferred_channel(self):
        """Test sending notification with preferred channel."""
        request = NotificationRequest(
            user_id="test_user",
            subject="Test Notification",
            content="This is a test notification.",
            preferred_channels=[NotificationChannel.EMAIL]
        )

        message_ids = self.service.send_notification(request)

        assert len(message_ids) == 1  # Only email

        message = self.service.get_notification_status(message_ids[0])
        assert message.channel == NotificationChannel.EMAIL

    def test_send_notification_no_user_preferences(self):
        """Test sending notification for user with no preferences."""
        request = NotificationRequest(
            user_id="non_existent_user",
            subject="Test Notification",
            content="This should fail."
        )

        message_ids = self.service.send_notification(request)
        assert len(message_ids) == 0

    def test_send_notification_no_contact_info(self):
        """Test sending notification for user with no contact info."""
        # User with enabled channels but no contact info
        user_prefs = UserPreferences(
            user_id="no_contact_user",
            enabled_channels=[NotificationChannel.EMAIL, NotificationChannel.SMS]
        )
        self.user_repo.save_user_preferences(user_prefs)

        request = NotificationRequest(
            user_id="no_contact_user",
            subject="Test Notification",
            content="This should fail."
        )

        message_ids = self.service.send_notification(request)
        assert len(message_ids) == 0

    def test_send_notification_provider_unavailable(self):
        """Test sending notification when provider is unavailable."""
        # Make email provider unavailable
        self.email_provider.set_availability(False)

        request = NotificationRequest(
            user_id="test_user",
            subject="Test Notification",
            content="This should only go via SMS."
        )

        message_ids = self.service.send_notification(request)

        assert len(message_ids) == 1  # Only SMS should work

        message = self.service.get_notification_status(message_ids[0])
        assert message.channel == NotificationChannel.SMS


class TestInputValidation:
    """Test input validation across the system."""

    def test_email_validation(self):
        """Test email address validation."""
        provider = EmailProvider()

        message = NotificationMessage(
            message_id="test_msg",
            user_id="test_user",
            subject="Test",
            content="Test content",
            channel=NotificationChannel.EMAIL,
            recipient="invalid_email",  # No @ symbol
            created_at=datetime.now()
        )

        result = provider.send_notification(message)
        assert result is False

    def test_phone_validation(self):
        """Test phone number validation."""
        provider = SMSProvider()

        message = NotificationMessage(
            message_id="test_msg",
            user_id="test_user",
            subject="Test",
            content="Test content",
            channel=NotificationChannel.SMS,
            recipient="**********",  # No + prefix
            created_at=datetime.now()
        )

        result = provider.send_notification(message)
        assert result is False


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
