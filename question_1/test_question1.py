"""
Comprehensive test suite for the idempotency key implementation.

Tests cover:
- Basic idempotency functionality
- Concurrent request handling
- Error scenarios
- Edge cases
- Performance characteristics
"""

import pytest
import json
import time
from fastapi.testclient import TestClient
from fastapi import HTTPException
from unittest.mock import Mock, patch
import redis
from solution import app, IdempotencyService

# Test client
client = TestClient(app)

# Test data
VALID_PAYMENT_REQUEST = {
    "amount": 100.0,
    "currency": "USD",
    "card_token": "tok_test_123"
}

VALID_HEADERS = {
    "Idempotency-Key": "test-key-123",
    "Content-Type": "application/json"
}

class TestIdempotencyBasics:
    """Test basic idempotency functionality."""
    
    def test_successful_payment_creation(self):
        """Test successful payment creation with idempotency key."""
        response = client.post(
            "/payments",
            json=VALID_PAYMENT_REQUEST,
            headers=VALID_HEADERS
        )
        
        assert response.status_code == 201
        data = response.json()
        assert "transaction_id" in data
        assert data["status"] == "approved"
        assert data["amount"] == 100.0
        assert data["currency"] == "USD"
    
    def test_idempotent_request_returns_same_response(self):
        """Test that duplicate requests return identical responses."""
        # First request
        response1 = client.post(
            "/payments",
            json=VALID_PAYMENT_REQUEST,
            headers={**VALID_HEADERS, "Idempotency-Key": "duplicate-test-1"}
        )
        
        # Second identical request
        response2 = client.post(
            "/payments",
            json=VALID_PAYMENT_REQUEST,
            headers={**VALID_HEADERS, "Idempotency-Key": "duplicate-test-1"}
        )
        
        assert response1.status_code == 201
        assert response2.status_code == 201
        assert response1.json() == response2.json()
    
    def test_different_body_with_same_key_fails(self):
        """Test that reusing idempotency key with different body fails."""
        # First request
        client.post(
            "/payments",
            json=VALID_PAYMENT_REQUEST,
            headers={**VALID_HEADERS, "Idempotency-Key": "body-change-test"}
        )
        
        # Second request with different body
        different_request = {**VALID_PAYMENT_REQUEST, "amount": 200.0}
        response = client.post(
            "/payments",
            json=different_request,
            headers={**VALID_HEADERS, "Idempotency-Key": "body-change-test"}
        )
        
        assert response.status_code == 422
        assert "different request body" in response.json()["detail"]
    
    def test_missing_idempotency_key_fails(self):
        """Test that missing idempotency key returns error."""
        response = client.post(
            "/payments",
            json=VALID_PAYMENT_REQUEST,
            headers={"Content-Type": "application/json"}
        )
        
        assert response.status_code == 422  # FastAPI validation error
    
    def test_empty_idempotency_key_fails(self):
        """Test that empty idempotency key returns error."""
        response = client.post(
            "/payments",
            json=VALID_PAYMENT_REQUEST,
            headers={**VALID_HEADERS, "Idempotency-Key": ""}
        )

        assert response.status_code == 400
        assert "cannot be empty" in response.json()["detail"]

    def test_too_long_idempotency_key_fails(self):
        """Test that idempotency key longer than 255 characters fails."""
        long_key = "a" * 256  # 256 characters
        response = client.post(
            "/payments",
            json=VALID_PAYMENT_REQUEST,
            headers={**VALID_HEADERS, "Idempotency-Key": long_key}
        )

        assert response.status_code == 400
        assert "255 characters or less" in response.json()["detail"]

    def test_invalid_characters_in_idempotency_key_fails(self):
        """Test that idempotency key with invalid characters fails."""
        invalid_keys = [
            "key with spaces",
            "key@with#special$chars",
            "key/with/slashes",
            "key.with.dots"
        ]

        for invalid_key in invalid_keys:
            response = client.post(
                "/payments",
                json=VALID_PAYMENT_REQUEST,
                headers={**VALID_HEADERS, "Idempotency-Key": invalid_key}
            )

            assert response.status_code == 400
            assert "invalid characters" in response.json()["detail"]

    def test_valid_idempotency_key_formats(self):
        """Test that valid idempotency key formats work."""
        import time
        timestamp = str(int(time.time() * 1000))  # Unique timestamp

        valid_keys = [
            f"simple-key-{timestamp}",
            f"key_with_underscores-{timestamp}",
            f"KEY123-{timestamp}",
            f"uuid-like-12345678-1234-1234-1234-123456789012-{timestamp}",
            f"{'a' * 200}-{timestamp}"  # Long but valid length
        ]

        for valid_key in valid_keys:
            response = client.post(
                "/payments",
                json=VALID_PAYMENT_REQUEST,
                headers={**VALID_HEADERS, "Idempotency-Key": valid_key}
            )

            assert response.status_code == 201, f"Failed for key: {valid_key}, response: {response.json()}"

class TestRequestValidation:
    """Test request validation and error handling."""
    
    def test_invalid_amount_fails(self):
        """Test that negative amount fails validation."""
        invalid_request = {**VALID_PAYMENT_REQUEST, "amount": -10.0}
        response = client.post(
            "/payments",
            json=invalid_request,
            headers=VALID_HEADERS
        )
        
        assert response.status_code == 422
    
    def test_invalid_currency_fails(self):
        """Test that invalid currency fails validation."""
        invalid_request = {**VALID_PAYMENT_REQUEST, "currency": "INVALID"}
        response = client.post(
            "/payments",
            json=invalid_request,
            headers=VALID_HEADERS
        )
        
        assert response.status_code == 422
    
    def test_missing_card_token_fails(self):
        """Test that missing card token fails validation."""
        invalid_request = {k: v for k, v in VALID_PAYMENT_REQUEST.items() if k != "card_token"}
        response = client.post(
            "/payments",
            json=invalid_request,
            headers=VALID_HEADERS
        )
        
        assert response.status_code == 422

class TestIdempotencyService:
    """Test the IdempotencyService class directly."""
    
    @pytest.fixture
    def mock_redis(self):
        """Mock Redis client for testing."""
        return Mock(spec=redis.Redis)
    
    @pytest.fixture
    def service(self, mock_redis):
        """IdempotencyService instance with mocked Redis."""
        return IdempotencyService(mock_redis)
    
    def test_hash_request_consistency(self, service):
        """Test that request hashing is consistent."""
        body1 = {"amount": 100.0, "currency": "USD", "card_token": "tok_123"}
        body2 = {"currency": "USD", "amount": 100.0, "card_token": "tok_123"}  # Different order
        
        hash1 = service._hash_request(body1)
        hash2 = service._hash_request(body2)
        
        assert hash1 == hash2  # Should be identical despite different key order
        assert len(hash1) == 64  # SHA-256 hex digest length
    
    def test_cache_key_generation(self, service):
        """Test cache key generation."""
        key = service._generate_cache_key("test-key")
        assert key == "idem:test-key"
    
    def test_lock_key_generation(self, service):
        """Test lock key generation."""
        key = service._generate_lock_key("test-key")
        assert key == "idem:test-key:lock"
    
    def test_get_cached_response_not_found(self, service, mock_redis):
        """Test getting cached response when not found."""
        mock_redis.get.return_value = None
        
        result = service.get_cached_response("test-key", "hash123")
        
        assert result is None
        mock_redis.get.assert_called_once_with("idem:test-key")
    
    def test_get_cached_response_found(self, service, mock_redis):
        """Test getting cached response when found."""
        cached_data = {
            "body_hash": "hash123",
            "response_json": '{"status": "success"}',
            "http_status": 201
        }
        mock_redis.get.return_value = json.dumps(cached_data)
        
        result = service.get_cached_response("test-key", "hash123")
        
        assert result == cached_data
    
    def test_acquire_lock_success(self, service, mock_redis):
        """Test successful lock acquisition."""
        mock_redis.set.return_value = True
        
        result = service.acquire_lock("test-key")
        
        assert result is True
        mock_redis.set.assert_called_once()
    
    def test_acquire_lock_failure(self, service, mock_redis):
        """Test failed lock acquisition."""
        mock_redis.set.return_value = False

        result = service.acquire_lock("test-key")

        assert result is False

    def test_validate_idempotency_key_valid(self, service):
        """Test validation of valid idempotency keys."""
        valid_keys = [
            "simple-key",
            "key_with_underscores",
            "KEY123",
            "uuid-12345678-1234-1234-1234-123456789012",
            "a" * 255  # Maximum length
        ]

        for key in valid_keys:
            result = service.validate_idempotency_key(key)
            assert result == key.strip()

    def test_validate_idempotency_key_invalid_empty(self, service):
        """Test validation fails for empty keys."""
        with pytest.raises(HTTPException) as exc_info:
            service.validate_idempotency_key("")
        assert exc_info.value.status_code == 400
        assert "cannot be empty" in exc_info.value.detail

    def test_validate_idempotency_key_invalid_too_long(self, service):
        """Test validation fails for keys that are too long."""
        long_key = "a" * 256
        with pytest.raises(HTTPException) as exc_info:
            service.validate_idempotency_key(long_key)
        assert exc_info.value.status_code == 400
        assert "255 characters or less" in exc_info.value.detail

    def test_validate_idempotency_key_invalid_characters(self, service):
        """Test validation fails for invalid characters."""
        invalid_keys = [
            "key with spaces",
            "<EMAIL>",
            "key/path/here",
            "key.with.dots"
        ]

        for key in invalid_keys:
            with pytest.raises(HTTPException) as exc_info:
                service.validate_idempotency_key(key)
            assert exc_info.value.status_code == 400
            assert "invalid characters" in exc_info.value.detail

class TestConcurrency:
    """Test concurrent request handling."""

    def test_concurrent_requests_same_key(self):
        """Test that concurrent requests with same key are handled correctly."""
        import threading
        import queue

        results = queue.Queue()

        def make_request():
            response = client.post(
                "/payments",
                json=VALID_PAYMENT_REQUEST,
                headers={**VALID_HEADERS, "Idempotency-Key": "concurrent-test"}
            )
            results.put(response.json())

        # Start multiple threads with same idempotency key
        threads = []
        for _ in range(5):
            thread = threading.Thread(target=make_request)
            threads.append(thread)
            thread.start()

        # Wait for all threads to complete
        for thread in threads:
            thread.join()

        # Collect results
        responses = []
        while not results.empty():
            responses.append(results.get())

        # All responses should be identical
        assert len(responses) == 5
        first_response = responses[0]
        for response in responses[1:]:
            assert response == first_response

class TestHealthCheck:
    """Test health check endpoint."""
    
    def test_health_check_success(self):
        """Test successful health check."""
        response = client.get("/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "timestamp" in data
        assert "redis" in data

class TestErrorScenarios:
    """Test various error scenarios."""
    
    @patch('solution.redis')
    def test_redis_connection_failure(self, mock_redis):
        """Test handling of Redis connection failures."""
        mock_redis.get.side_effect = redis.ConnectionError("Connection failed")
        
        response = client.post(
            "/payments",
            json=VALID_PAYMENT_REQUEST,
            headers={**VALID_HEADERS, "Idempotency-Key": "redis-fail-test"}
        )
        
        assert response.status_code == 503
        assert "temporarily unavailable" in response.json()["detail"]

class TestPerformance:
    """Test performance characteristics."""
    
    def test_cache_hit_performance(self):
        """Test that cache hits are fast."""
        # First request to populate cache
        client.post(
            "/payments",
            json=VALID_PAYMENT_REQUEST,
            headers={**VALID_HEADERS, "Idempotency-Key": "perf-test"}
        )
        
        # Measure cache hit performance
        start_time = time.time()
        response = client.post(
            "/payments",
            json=VALID_PAYMENT_REQUEST,
            headers={**VALID_HEADERS, "Idempotency-Key": "perf-test"}
        )
        end_time = time.time()
        
        assert response.status_code == 201
        assert (end_time - start_time) < 0.1  # Should be very fast (< 100ms)

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
