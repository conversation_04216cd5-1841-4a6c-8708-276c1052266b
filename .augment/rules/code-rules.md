---
type: "always_apply"
---

# Clean Code Guidelines

## 1. Separation of Concerns (SOC)

- Break down complex programs into smaller, focused units
- Each component/module should have a single responsibility
- Keep layers (UI, business logic, data) separate
- Use meaningful folder structure to organize code
- Create reusable components and utilitiesSOC

## 2. Document Your Code (DYC)

- Write clear, descriptive comments explaining complex logic
- Use JSDoc for function/component documentation
- Keep README files up-to-date
- Include examples in documentation
- Document configuration and environment setup
- Add inline comments for non-obvious code sections

## 3. Don't Repeat Yourself (DRY)

- Extract common functionality into reusable functions/components
- Use constants for repeated values
- Create shared utilities and hooks
- Implement design patterns to avoid duplication
- Maintain a component library for common UI elements

## 4. Keep It Simple, Stupid (KISS)

- Write clear, straightforward code
- Avoid over-engineering solutions
- Use descriptive variable and function names
- Break complex functions into smaller, focused ones
- Minimize dependencies and complexity

## 5. Test Driven Development (TDD)

- Write tests before implementing features
- Follow Red-Green-Refactor cycle
- Maintain high test coverage
- Write unit, integration, and e2e tests
- Use testing best practices and patterns
- Keep tests readable and maintainable

## 6. You Ain't Gonna Need It (YAGNI)

- Only implement required features
- Avoid speculative programming
- Remove unused code and dependencies
- Focus on current requirements
- Plan for extensibility without over-engineering

## 7. Problem Prevention (PP)

- Run static code analysis regularly
- Use linting tools and fix warnings
- Follow code review best practices
- Document known issues and solutions
- Maintain error logging and monitoring
- Implement automated testing pipelines
- Set up continuous integration checks
- Use code quality metrics and thresholds
- Conduct regular security audits
- Practice defensive programming



## 8. Single Source of Truth (SSOT)
- Maintain one authoritative source for each piece of data
- Avoid data duplication across components/modules
- Use centralized state management
- Implement proper data synchronization
- Create clear data flow patterns
- Document data ownership and access patterns


## 9. Incremental Development (ID)

- Break features into small, testable increments
- Update documentation with each increment
- Use open-source tools for automated issue detection
- Map code dependencies for impact analysis
- Apply fixes iteratively with validation

Quick Reference:
┌─────────────────┐
│ Plan → Code → Test │
│ ↑ ↓ │
│ └── Document │
└─────────────────┘

Key Steps:

1. Small changes
2. Doc updates
3. Auto-detect issues
4. Impact check
5. Validate fixes

## 10. Definition of Done (DoD) for Coding Implementation:

Code is complete and compiles without errors
- All acceptance criteria are met
- Self-review done (logic, edge cases, performance)
- Unit tests are written and pass
- No critical/high severity bugs
- Code follows coding standards/style guide
- Documentation (if needed) is updated
- Security review completed (input validation, no secrets, auth/authz, data protection)
- Static analysis/linting clean
- Potential issues fixed (e.g. memory, race, error handling)

## 11. API Design Best Practices (API)

### RESTful API Design
- Use resource-based architecture with clear noun-based endpoints
- Follow HTTP method conventions: GET (read), POST (create), PUT (update), DELETE (remove)
- Use plural nouns for collections (e.g., `/users`, `/products`)
- Implement proper status codes (200, 201, 400, 404, 500)
- Design consistent URI patterns and avoid verbs in endpoints

### Request/Response Handling
- Implement content negotiation (Accept headers)
- Use JSON as primary data format
- Provide meaningful error messages with error codes
- Support pagination for large datasets (`?page=1&limit=10`)
- Include filtering and sorting capabilities (`?category=electronics&sort=price`)

### API Security & Performance
- Implement OAuth 2.0 or JWT for authentication
- Use HTTPS for all API communications
- Apply rate limiting to prevent abuse
- Implement request/response compression (gzip)
- Use caching strategies (ETags, Cache-Control headers)
- Validate all input data and sanitize outputs

### Documentation & Versioning
- Use OpenAPI/Swagger for interactive documentation
- Implement semantic versioning (v1.2.3)
- Provide code examples in multiple languages
- Maintain clear changelog for API updates
- Support backward compatibility when possible

## 12. Database Design Excellence (DB)

### Normalization & Structure
- Apply appropriate normalization (1NF, 2NF, 3NF) to reduce redundancy
- Use denormalization strategically for performance-critical queries
- Design clear entity relationships with proper foreign keys
- Implement consistent naming conventions for tables and columns
- Use appropriate data types and constraints

### Performance Optimization
- Create indexes on frequently queried columns
- Avoid over-indexing to prevent write performance degradation
- Use composite indexes for multi-column queries
- Implement query optimization and execution plan analysis
- Consider partitioning for large tables

### Data Integrity & Security
- Implement proper constraints (NOT NULL, UNIQUE, CHECK)
- Use transactions for data consistency
- Apply principle of least privilege for database access
- Encrypt sensitive data at rest and in transit
- Implement audit trails for critical data changes
- Regular backup and recovery testing

### Scalability Considerations
- Design for horizontal scaling when needed
- Use connection pooling for efficient resource usage
- Implement read replicas for read-heavy workloads
- Consider caching layers (Redis, Memcached)
- Monitor database performance metrics regularly

## 13. Security Best Practices (SEC)

### OWASP Top 10 API Security (2023)
- **API1**: Implement proper object-level authorization
- **API2**: Secure authentication mechanisms (no weak tokens)
- **API3**: Validate authorization at property level
- **API4**: Implement resource consumption limits
- **API5**: Enforce function-level authorization
- **API6**: Protect sensitive business flows from automation
- **API7**: Prevent Server-Side Request Forgery (SSRF)
- **API8**: Secure configuration management
- **API9**: Maintain proper API inventory and documentation
- **API10**: Validate third-party API consumption

### Authentication & Authorization
- Use strong password policies and multi-factor authentication
- Implement proper session management
- Apply role-based access control (RBAC)
- Use secure token storage and transmission
- Implement proper logout and session timeout

### Data Protection
- Encrypt sensitive data at rest and in transit
- Use HTTPS/TLS for all communications
- Implement proper input validation and output encoding
- Apply data masking for non-production environments
- Follow data retention and deletion policies

### Secure Development
- Conduct regular security code reviews
- Use static application security testing (SAST)
- Implement dynamic application security testing (DAST)
- Keep dependencies updated and scan for vulnerabilities
- Follow secure coding standards and guidelines

## 14. Home Test & Assessment Excellence (HT)

### Take-Home Test Design
- Time-box challenges to 3-4 hours of focused work
- Base problems on real-world scenarios from your domain
- Provide clear requirements and evaluation criteria
- Allow 2-7 days for completion to respect candidate schedules
- Include setup instructions and expected deliverables

### Evaluation Framework
- **Code Functionality** (25%): Correctness, edge cases, performance
- **Code Quality** (30%): Readability, structure, maintainability
- **Testing Approach** (20%): Coverage, test structure, methodology
- **Documentation** (15%): Clarity, decision justification
- **Technical Choices** (10%): Appropriate tools and patterns

### Assessment Best Practices
- Create structured evaluation rubrics for consistency
- Focus on problem-solving approach over perfect solutions
- Provide follow-up discussions about implementation decisions
- Share evaluation criteria with candidates beforehand
- Combine take-home tests with brief technical discussions

### Candidate Experience
- Respect candidate time with reasonable scope
- Provide clear communication and expectations
- Offer feedback regardless of outcome
- Ensure assessment reflects actual job requirements
- Create inclusive evaluation processes that reduce bias

## 15. Coding Assessment Solution Excellence (CAS)

### Production-Ready Implementation Standards
- **Enterprise Architecture**: Use service classes with dependency injection patterns
- **Comprehensive Error Handling**: Cover all failure scenarios with appropriate HTTP status codes
- **Input Validation**: Implement robust validation with clear error messages
- **Configuration Management**: Use environment variables for all configurable settings
- **Health Monitoring**: Include health check endpoints with dependency status
- **Structured Logging**: Implement correlation IDs and structured log formats

### Code Organization & Structure
- **Single Responsibility**: Each class/method should have one clear purpose
- **Separation of Concerns**: Separate validation, business logic, and data access
- **Clean Interfaces**: Use abstract base classes or protocols for testability
- **Dependency Injection**: Make dependencies explicit and injectable
- **Consistent Naming**: Use descriptive names that reflect business domain
- **Modular Design**: Organize code into logical modules with clear boundaries

### Validation & Security Best Practices
- **Input Sanitization**: Validate all inputs according to business rules
- **Industry Standards**: Follow domain-specific validation patterns (e.g., banking, healthcare)
- **Security Headers**: Validate header formats and prevent injection attacks
- **Data Integrity**: Use cryptographic hashing for request integrity verification
- **Rate Limiting**: Implement appropriate throttling mechanisms
- **Audit Logging**: Log all security-relevant events with sufficient detail

### Concurrency & Distributed Systems
- **Distributed Locking**: Use Redis/database locks for critical sections
- **Idempotency**: Implement proper idempotency mechanisms for state-changing operations
- **Timeout Handling**: Set appropriate timeouts with graceful degradation
- **Retry Logic**: Implement exponential backoff for transient failures
- **Circuit Breakers**: Protect against cascading failures
- **Eventual Consistency**: Handle distributed state appropriately

### Testing Excellence
- **Comprehensive Coverage**: Test happy path, edge cases, and error scenarios
- **Unit Tests**: Test individual components in isolation with mocks
- **Integration Tests**: Test component interactions and external dependencies
- **Concurrency Tests**: Test race conditions and concurrent access patterns
- **Performance Tests**: Validate response times and resource usage
- **Security Tests**: Test input validation and security boundaries

### Documentation Standards
- **API Documentation**: Provide complete API reference with examples
- **Architecture Decisions**: Document key design choices and trade-offs
- **Setup Instructions**: Include clear environment setup and dependency installation
- **Usage Examples**: Provide realistic usage scenarios and code samples
- **Error Handling**: Document all error conditions and response formats
- **Configuration Guide**: Explain all environment variables and settings

### Performance & Scalability
- **Caching Strategy**: Implement appropriate caching with TTL management
- **Database Optimization**: Use proper indexing and query optimization
- **Memory Management**: Avoid memory leaks and implement proper cleanup
- **Connection Pooling**: Use connection pools for external resources
- **Async Processing**: Use async/await patterns where appropriate
- **Resource Monitoring**: Include metrics for key performance indicators

### Error Handling Excellence
- **Meaningful Messages**: Provide clear, actionable error messages
- **Proper Status Codes**: Use appropriate HTTP status codes for each scenario
- **Error Context**: Include relevant context without exposing sensitive data
- **Graceful Degradation**: Handle partial failures appropriately
- **Recovery Mechanisms**: Implement automatic recovery where possible
- **Error Correlation**: Use correlation IDs for distributed error tracking

### Code Quality Metrics
- **Cyclomatic Complexity**: Keep methods simple with low complexity scores
- **Test Coverage**: Maintain >90% test coverage for critical paths
- **Code Duplication**: Eliminate duplicate code through proper abstraction
- **Dependency Management**: Minimize external dependencies and keep them updated
- **Static Analysis**: Use linting tools and fix all warnings
- **Type Safety**: Use type hints and validation for better code reliability

### Assessment-Specific Requirements
- **Requirements Traceability**: Ensure every requirement is implemented and tested
- **Edge Case Handling**: Demonstrate thorough consideration of edge cases
- **Production Readiness**: Include all features needed for production deployment
- **Code Review Ready**: Write code that would pass a rigorous code review
- **Maintainability**: Structure code for easy modification and extension
- **Operational Excellence**: Include monitoring, logging, and debugging capabilities