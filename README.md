BACKEND ENGINEER HOME TEST
Read this first:
1. This take-home test could be finished in 4-6 hours. Take your time and try to make it as good as you
can, just let us know if you need more time. Hopefully, you can learn some new things through the
exam!
2. We're looking for a Java or Python developer, so please use either of these languages to complete
the test. Feel free to choose the one you’re most comfortable with - Java or Python.


HOME TEST
Question 1:
Using Java or Python, implement an idempotency key mechanism for a RESTful API that processes
payment transactions.
Your implementation should:
1. Accept an idempotency key in API requests
2. Store the request and response associated with each idempotency key
3. Return the payment transaction response when the same idempotency key is reused
4. Handle concurrent requests with the same idempotency key correctly
5. Set appropriate expiration for stored idempotency keys

Question 2:
Using Java or Python, implement a notification system using that can:
1. Support multiple notification channels including Email and SMS
2. Select the appropriate notification channel based on user preferences like if user enable Email as
their notification option, use Email

* Note: Assumption: Email and SMS provider will be available via 3rd party providers so you don’t need to write
the actual code to send the notification through those channels

Question 3:
If you are the reviewer of this code block, what’s your thinking?

```python
import time
class StripePaymentProcessor:
    def process_stripe_payment(self, amount, card_number):
        print(f"Connecting to Stripe API...")
        print(f"Processing ${amount} payment with Stripe")
        return f"stripe-tx-{int(time.time())}"
class EmailSender:
    def send_confirmation(self, email, tx_id, amount):
        print(f"Sending payment confirmation to {email}")
class TransactionService:
    def process_transaction(self, amount, card_number, email):
        tx_id = self.stripe_processor.process_stripe_payment(amount, card_number)
        self.email_sender.send_confirmation(email, tx_id, amount)
        return tx_id
# Main Application
def main():
    service = TransactionService()
    tx_id = service.process_transaction(99.99, "dummy-number", "<EMAIL>")
if __name__ == "__main__":
    main()
```


Question 4:
You're working on a financial transaction system with the following schema:
```sql
CREATE TABLE transactions (
    transaction_id UUID PRIMARY KEY,
    account_number VARCHAR(50),
    transaction_amount DECIMAL(19, 4),
    transaction_type VARCHAR(20),
    booking_date DATE,
    entry_date TIMESTAMP
);

CREATE INDEX idx_account_number ON transactions(account_number);
```

The system has grown significantly over time and now contains:
• Over 10 million account numbers
• Some accounts have more than 1 million transactions
• The total table size exceeds several billion rows
Currently, the application uses this query to retrieve paginated transactions for a specific account:

```sql
SELECT * FROM transactions
WHERE account_number = ?
ORDER BY entry_date DESC
LIMIT 20 OFFSET ?;
```

This query has become increasingly slow as the system has grown, particularly for accounts with large
numbers of transactions.
Provide suggestion to optimize this query


Question 5:
Giving this code block

```python
import logging
from datetime import datetime
from decimal import Decimal
class TransferService:
    def __init__(self, account_repository):
        self.account_repository = account_repository
        self.logger = logging.getLogger(__name__)
    def transfer_money(self, from_account_id: str, to_account_id: str, amount: Decimal) ->bool:
        try:
            # Find source account and check balance
            source_account = self.account_repository.find_by_id(from_account_id)
            if not source_account:
                raise RuntimeError("Source account not found")
            if source_account.balance < amount:
                self.logger.error(f"Insufficient funds in account {from_account_id}")
                return False
            # Find destination account
            destination_account = self.account_repository.find_by_id(to_account_id)
            if not destination_account:
                raise RuntimeError("Destination account not found")
            # Update source account balance
            source_account.balance = source_account.balance - amount
            source_account.last_updated = datetime.now()
            self.account_repository.save(source_account)
            # Update destination account balance
            destination_account.balance = destination_account.balance + amount
            destination_account.last_updated = datetime.now()
            self.account_repository.save(destination_account)
            self.logger.info(f"Transfer of {amount} from account {from_account_id} to {to_account_id} completed successfully")
            return True
        except Exception as e:
            self.logger.error(f"Error during transfer: {str(e)}", exc_info=True)
        return False
```

Additional context:
• The application runs on multiple servers behind a load balancer
• During peak times, the system processes hundreds of transfers per second
• Some accounts (like corporate accounts) are involved in many concurrent transfers"
What specific issues would you identify in this code, and how would you fix them?


Question 6:

```python
from flask import Flask, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from database import db
app = Flask(__name__)
class TransferService:
def execute_transfer(self, source_account_id, destination_account_id, amount, description):
    # Simulated transfer logic
    transaction_id = "TXN12345"
    status = "COMPLETED"
    return {"transaction_id": transaction_id, "status": status}
transfer_service = TransferService()
@app.route("/api/transfers/execute", methods=["POST"])
@jwt_required()
def execute_transfer():
    # Get the authenticated user's profile ID from JWT
    current_profile_id = get_jwt_identity()
    # Get the transfer details from the request
    data = request.get_json()
    source_account_id = data.get("source_account_id")
    destination_account_id = data.get("destination_account_id")
    amount = data.get("amount")
    description = data.get("description")
    # Retrieve source and destination accounts
    source_account = db.session.query("SELECT * FROM accounts WHERE id = %s", (source_account_id,)).fetchone()
    destination_account = db.session.query("SELECT * FROM accounts WHERE id = %s", (destination_account_id,)).fetchone()
    result = transfer_service.execute_transfer(
        source_account_id,
        destination_account_id,
        amount,
        description
    )
    return jsonify(result)
if __name__ == "__main__":
    app.run(debug=True)
```


Background
You are reviewing a Python implementation of a funds transfer API endpoint. In this system, a Profile
represents a user, and each Profile can own multiple Accounts. Users should only be able to transfer money
from accounts they own.
Task
Review the following code and identify any security vulnerabilities. Explain what the vulnerability is,
potential consequences, and how you would fix it.

Question 7: Please pick 1 option to complete

Background
You are tasked with designing a notification system for a large-scale application. The system needs to
support multiple notification channels (email, SMS, push notifications) with different priority levels. The
system should be scalable, reliable, and able to handle high volumes of notifications.

Requirements
Functional Requirements:
1. Support multiple notification channels:
    - Email
    - SMS
    - Push notifications
    - In-app notifications

2. Implement priority levels for notifications (e.g., critical, high, medium, low)
3. Allow users to set preferences for notification channels based on notification types
4. Support throttling and rate limiting to prevent notification spam
5. Provide delivery status tracking (sent, delivered, failed, read)
6. Handle notification retries for failed deliveries with configurable retry policies


Non-Functional Requirements:
1. High availability (99.9% uptime)
2. Scalability to handle millions of notifications per day
3. Low latency for high-priority notifications
4. Fault tolerance and graceful degradation
5. Support for future expansion to additional notification channels
