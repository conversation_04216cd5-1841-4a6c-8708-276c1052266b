#!/usr/bin/env python3
"""
Database models and session management for the transfer API.
"""

import logging
from decimal import Decimal
from datetime import datetime, timezone
from dataclasses import dataclass
from typing import Optional
from contextlib import contextmanager

logger = logging.getLogger(__name__)

@dataclass
class Account:
    """
    Account model representing user bank accounts.

    This model includes security-focused fields for ownership validation
    and precise monetary calculations.

    Attributes:
        id (str): Unique account identifier
        profile_id (str): User/Profile ID for ownership validation (prevents BOLA/IDOR)
        balance (Decimal): Account balance with precise decimal arithmetic
        last_updated (datetime): UTC timestamp of last account modification
        currency (str): Account currency code (default: USD)
        status (str): Account status (ACTIVE, SUSPENDED, CLOSED)
    """
    id: str
    profile_id: str
    balance: Decimal
    last_updated: datetime
    currency: str = "USD"
    status: str = "ACTIVE"

@dataclass
class Transfer:
    """
    Transfer model for transaction audit records.

    This model provides complete audit trail and idempotency support
    for financial transactions.

    Attributes:
        id (str): Unique transfer identifier
        source_account_id (str): ID of account money is transferred from
        destination_account_id (str): ID of account money is transferred to
        amount (Decimal): Transfer amount with precise decimal arithmetic
        description (str): Human-readable transfer description
        initiated_by (str): Profile ID of user who initiated the transfer
        idempotency_key (str): Unique key for duplicate request prevention
        created_at (datetime): UTC timestamp when transfer was created
        status (str): Transfer status (COMPLETED, PENDING, FAILED)
    """
    id: str
    source_account_id: str
    destination_account_id: str
    amount: Decimal
    description: str
    initiated_by: str
    idempotency_key: str
    created_at: datetime
    status: str = "COMPLETED"

class DatabaseSession:
    """
    Database session for managing data operations with transaction support.

    This class provides a simplified interface that mimics SQLAlchemy's session
    management with proper transaction handling and rollback capabilities.
    """

    def __init__(self):
        """Initialize database session with in-memory storage for demonstration."""
        # In real implementation: initialize database connection pool
        self.accounts = {}
        self.transfers = {}
        self._in_transaction = False

    @contextmanager
    def begin(self):
        """
        Begin database transaction with automatic commit/rollback.

        Provides ACID transaction semantics:
        - Atomicity: All operations succeed or all fail
        - Consistency: Database remains in valid state
        - Isolation: Concurrent transactions don't interfere
        - Durability: Committed changes are permanent

        Yields:
            DatabaseSession: The session instance for chaining operations

        Raises:
            Exception: Re-raises any exception that occurs during transaction
        """
        # In real implementation: BEGIN TRANSACTION
        self._in_transaction = True
        try:
            yield self
            # In real implementation: COMMIT
            logger.debug("Transaction committed")
        except Exception as e:
            # In real implementation: ROLLBACK
            logger.error(f"Transaction rolled back: {e}")
            raise
        finally:
            self._in_transaction = False

    def query(self, model_class):
        """
        Create query builder for specified model class.

        Args:
            model_class: The model class to query (Account or Transfer)

        Returns:
            Query: Query builder instance for chaining operations
        """
        return Query(model_class, self)

    def add(self, obj):
        """
        Add object to session for persistence.

        Args:
            obj: Model instance to add (Account or Transfer)
        """
        # In real implementation: session.add(obj)
        if isinstance(obj, Transfer):
            self.transfers[obj.id] = obj
        logger.debug(f"Added {type(obj).__name__} to session")

class Query:
    """Query builder for database operations."""
    
    def __init__(self, model_class, session):
        self.model_class = model_class
        self.session = session
        self._filters = {}
    
    def filter_by(self, **kwargs):
        """Add filter conditions to query."""
        self._filters.update(kwargs)
        return self
    
    def with_for_update(self):
        """Add row-level locking to query."""
        # In real implementation: adds FOR UPDATE clause
        logger.debug("Row-level locking applied")
        return self
    
    def first(self):
        """Execute query and return first result."""
        # In real implementation: execute query and return first result
        if self.model_class == Account:
            for account in self.session.accounts.values():
                if all(getattr(account, k) == v for k, v in self._filters.items()):
                    return account
        elif self.model_class == Transfer:
            for transfer in self.session.transfers.values():
                if all(getattr(transfer, k) == v for k, v in self._filters.items()):
                    return transfer
        return None

class Database:
    """Database interface."""
    def __init__(self):
        self.session = DatabaseSession()

# Global database instance
db = Database()

def init_sample_data():
    """Initialize sample data for demonstration."""
    accounts = [
        Account("acc_001", "profile_123", Decimal("1000.00"), datetime.now(timezone.utc)),
        Account("acc_002", "profile_456", Decimal("500.00"), datetime.now(timezone.utc)),
        Account("acc_003", "profile_789", Decimal("0.00"), datetime.now(timezone.utc)),
    ]
    
    for account in accounts:
        db.session.accounts[account.id] = account
    
    logger.info("Sample data initialized")

# Initialize sample data
init_sample_data()
