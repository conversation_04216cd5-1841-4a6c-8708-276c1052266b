# Data Flow & Processing - Detailed Architecture Explanation

## System Architecture Overview

The notification system follows a microservices architecture with clear separation of concerns, implementing a queue-based message processing pipeline that ensures scalability, reliability, and fault tolerance.

## End-to-End Data Flow Analysis

### 1. **Request Initiation Layer (Left Side)**

**Components:**
- **SERVICE 1, SERVICE 2, SERVICE 3**: Upstream business services
- **Examples**: User Management Service, Payment Service, Order Management Service

**Flow:**
```
Business Event → HTTP POST Request → API Gateway
```

**Responsibilities:**
- Generate notification triggers based on business events
- Send structured notification requests with user context
- Include priority indicators and notification type metadata

**Example Payloads:**
```json
{
  "user_id": "12345",
  "notification_type": "payment_failure",
  "priority": "critical",
  "channels": ["push", "email", "sms"],
  "template_data": {
    "amount": "$99.99",
    "card_last_four": "1234"
  }
}
```

### 2. **Gateway & Security Layer**

**API Gateway:**
- **Load Balancing**: Distributes requests across notification service instances
- **SSL Termination**: Handles HTTPS encryption/decryption
- **Request Routing**: Routes to appropriate service endpoints
- **Protocol Translation**: HTTP to internal messaging protocols

**Auth & Rate Limit Service:**
- **Authentication**: JWT token validation and service-to-service auth
- **Rate Limiting**: Multi-tier throttling (per-user, per-service, global)
- **Request Validation**: Schema validation and input sanitization
- **Circuit Breaker**: Protects downstream services from overload

**Flow Control Logic:**
```
Request → Auth Check → Rate Limit Check → Forward to Core Service
```

### 3. **Core Orchestration Layer (Notification Services)**

The notification service acts as the central orchestrator with four key sub-components:

#### **A) Priority & Dedup Engine**
**Functionality:**
- **Priority Assignment**: Maps notification types to priority levels
- **Deduplication**: Uses content hash + user ID to prevent duplicates
- **Business Rules**: Applies priority escalation rules

**Priority Mapping:**
```
Critical (P0): payment_failure, security_alert, system_outage
High (P1): order_confirmation, password_reset, account_locked  
Medium (P2): marketing_campaign, newsletter, feature_update
Low (P3): maintenance_notice, survey_request, tips
```

#### **B) Preference Services Integration**
**Database Query Flow:**
```
User ID → Preference DB → Channel Permissions → Filter Logic
```

**Preference Enforcement:**
- Queries user notification preferences from database
- Respects opt-out settings per channel type
- Applies quiet hours and frequency limits
- Handles consent management for GDPR compliance

#### **C) Throttle/Rate-Limit Component**
**Multi-Level Throttling:**
- **User Level**: Max notifications per user per hour/day
- **Channel Level**: Respect provider rate limits
- **System Level**: Overall throughput management
- **Content Level**: Prevent duplicate content flooding

#### **D) Dedup/Content Processing**
**Template Resolution:**
- Fetches message templates from template database
- Performs variable substitution with user data
- Handles localization and personalization
- Applies content formatting per channel requirements

### 4. **Message Queuing Layer (Center Section)**

**Queue Architecture:**
Each channel has dedicated priority queues with the following structure:

#### **iOS PN Queue**
```
Priority Structure: critical → high → medium → low
Message Format: APNs-compatible JSON payload
Retention: 24 hours with persistence
Partitioning: By user region for optimal delivery
```

#### **Android PN Queue**
```
Priority Structure: critical → high → medium → low  
Message Format: FCM-compatible JSON payload
Retention: 24 hours with persistence
Partitioning: By app version for compatibility
```

#### **SMS Queue**
```
Priority Structure: critical → high → medium → low
Message Format: Plain text with metadata
Retention: 72 hours for delivery attempts
Partitioning: By country code for provider routing
```

#### **Email Queue**
```
Priority Structure: critical → high → medium → low
Message Format: HTML/Plain text with attachments
Retention: 7 days for delivery attempts
Partitioning: By domain for reputation management
```

#### **In-App Queue**
```
Priority Structure: critical → high → medium → low
Message Format: JSON with UI metadata
Retention: 1 hour for real-time delivery
Partitioning: By active session status
```

**Queue Processing Logic:**
- **FIFO within Priority**: Messages processed in order within same priority
- **Priority Preemption**: Higher priority messages jump ahead
- **Batch Processing**: Similar messages grouped for efficiency
- **Back-pressure Handling**: Queue depth monitoring with alerts

### 5. **Worker Processing Layer (Right Side)**

**Channel-Specific Workers:**
Each worker type is optimized for its specific channel requirements:

#### **iOS PN Workers**
```
Queue Consumer → Message Transformation → APNs API Call → Status Update
```
- **Scaling**: Auto-scale based on queue depth (1-50 instances)
- **Retry Logic**: 3 attempts with exponential backoff (1s, 4s, 16s)
- **Error Handling**: Invalid tokens → user cleanup, rate limits → backoff

#### **Android Workers**  
```
Queue Consumer → FCM Payload Build → Firebase API → Delivery Receipt
```
- **Batch Support**: Up to 500 messages per API call
- **Topic Management**: Handles subscription-based notifications
- **Analytics**: Tracks delivery and engagement metrics

#### **SMS Workers**
```
Queue Consumer → Provider Selection → SMS API Call → Delivery Status
```
- **Multi-Provider**: Twilio, AWS SNS, MessageBird with failover
- **Cost Optimization**: Cheapest provider selection by destination
- **Compliance**: Handles opt-out keywords and regulations

#### **Email Workers**
```
Queue Consumer → Template Render → SMTP/API Send → Bounce Handling
```
- **Provider Rotation**: SendGrid, AWS SES, Mailgun for reputation
- **Content Processing**: HTML rendering, image optimization
- **Deliverability**: SPF, DKIM, DMARC validation

#### **In-App Workers**
```
Queue Consumer → WebSocket Lookup → Real-time Push → Acknowledgment
```
- **Connection Management**: Maintains active WebSocket connections
- **Fallback Strategy**: Store-and-forward for offline users
- **Real-time Delivery**: Sub-second delivery for active sessions

### 6. **Third-Party Provider Integration**

**Provider Communication:**
- **Connection Pooling**: Persistent connections to reduce latency
- **Health Monitoring**: Regular health checks with circuit breakers
- **SLA Monitoring**: Track response times and success rates
- **Failover Logic**: Automatic provider switching on failures

**Provider-Specific Handling:**
- **APNs**: Certificate-based auth, binary protocol optimization
- **FCM**: OAuth2 authentication, JSON payload optimization  
- **SMS Providers**: Webhook status updates, delivery receipts
- **Email Providers**: SMTP/API hybrid, bounce/complaint handling

### 7. **Status Tracking & Logging (Bottom Flow)**

**Notification Log Database:**
```
Schema: notification_id, user_id, channel, status, timestamp, metadata
Indexing: By user_id, timestamp, status for fast queries
Retention: 90 days for compliance, 1 year for analytics
```

**Status Webhook Service:**
- **Real-time Updates**: Immediate status updates to upstream services
- **Webhook Reliability**: Retry logic for webhook delivery failures
- **Status Aggregation**: Batch status updates for efficiency
- **Audit Trail**: Complete lifecycle tracking for compliance

**Delivery Status Flow:**
```
Provider Response → Status Parser → Database Update → Webhook Trigger → Upstream Service
```

### 8. **Error Handling & Recovery (Bottom Section)**

**Retry on Failure Logic:**
```
Worker Failure → Retry Counter Check → Exponential Backoff → Retry Attempt
                                   ↓ (Max Retries Exceeded)
                              Dead Letter Queue
```

**Dead Letter Queue System:**
- **Channel-Specific DLQs**: Separate queues for targeted analysis
- **Failure Classification**: Transient vs permanent failure categorization
- **Manual Recovery**: Admin interface for message reprocessing
- **Alert Integration**: Automatic alerts when DLQ thresholds exceeded

**Recovery Procedures:**
1. **Automated Recovery**: Transient failures auto-retry after cooldown
2. **Manual Investigation**: Permanent failures require human analysis  
3. **Bulk Reprocessing**: Tools for reprocessing batches of failed messages
4. **Root Cause Analysis**: Failure pattern analysis and system improvements

## Critical Performance Characteristics

### **Throughput Optimization**
- **Horizontal Scaling**: Each component scales independently
- **Queue Partitioning**: Parallel processing across partitions
- **Batch Processing**: Grouped operations where supported by providers
- **Connection Reuse**: Persistent connections to external services

### **Latency Optimization**  
- **Priority Processing**: Critical messages bypass normal queues
- **Regional Deployment**: Workers deployed close to provider endpoints
- **Caching Strategy**: User preferences and templates cached in Redis
- **Async Processing**: Non-blocking I/O throughout the pipeline

### **Reliability Features**
- **Message Persistence**: All queues persist messages to disk
- **Duplicate Prevention**: Idempotency keys prevent duplicate processing
- **Circuit Breakers**: Prevent cascade failures during outages
- **Health Monitoring**: Comprehensive health checks at every layer

This architecture ensures reliable, scalable notification delivery while maintaining user preferences, providing comprehensive error handling, and supporting high-volume processing requirements.
