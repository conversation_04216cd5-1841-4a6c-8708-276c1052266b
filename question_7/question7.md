Question 7: Please pick 1 option to complete

Background
You are tasked with designing a notification system for a large-scale application. The system needs to
support multiple notification channels (email, SMS, push notifications) with different priority levels. The
system should be scalable, reliable, and able to handle high volumes of notifications.

Requirements
Functional Requirements:
1. Support multiple notification channels:
    - Email
    - SMS
    - Push notifications
    - In-app notifications

2. Implement priority levels for notifications (e.g., critical, high, medium, low)
3. Allow users to set preferences for notification channels based on notification types
4. Support throttling and rate limiting to prevent notification spam
5. Provide delivery status tracking (sent, delivered, failed, read)
6. Handle notification retries for failed deliveries with configurable retry policies
